import os
from datetime import datetime, timedelta

from flask import Blueprint, jsonify, g, request, current_app
from app.core.comm_manager import comm_manager as manager, validate_action_items
from app.core.defaulters import defaulters
from app.core.ai import make_ai_client
from app.utils.supabase.queries import get_org

contacts_bp = Blueprint("contacts", __name__)


@contacts_bp.route("", methods=["POST", "OPTIONS"])
def contacts():
    if request.method == "OPTIONS":
        return jsonify({"methods": ["POST", "OPTIONS"]}), 200

    if request.method == "POST":
        print(f"[CONTACTS] Received POST request to /v0/contacts")
        print(f"[CONTACTS] Request data type: {type(request.json)}")
        print(f"[CONTACTS] Number of records in request: {len(request.json) if request.json else 0}")

        org_id = None
        # if g.user:
        #    org_id = g.user.user_metadata.get("org_id")

        if not org_id and os.environ.get("FLASK_DEBUG"):
            org_id = "8241d390-a8b5-4f59-b0a9-b95c074db3f5"
            print(f"[CONTACTS] Using debug org_id: {org_id}")

        print(f"[CONTACTS] Starting load_defaulters process...")
        try:
            # Load defaulters into the system
            result = defaulters.load_defaulters(request.json, org_id=org_id)
            print(f"[CONTACTS] load_defaulters completed with result: {result}")

            response_data = {
                "status": "success",
                "message": f"Processed {result['total_defaulters']} unique defaulters with {result['total_issues']} total issues",
                "defaulter_ids": result["defaulter_ids"],
                "total_defaulters": result["total_defaulters"],
                "total_issues": result["total_issues"],
                "background_processing": "Strategy generation and communication processing started in background",
                "note": "Heavy operations are being processed asynchronously with built-in thread-safe AI clients. Check /api/status/background-tasks for monitoring.",
            }

            # Include conflicts in response if any
            if result.get("conflicts"):
                response_data["data_conflicts"] = result["conflicts"]
                response_data["warning"] = (
                    f"Data conflicts detected for {len(result['conflicts'])} defaulters. Check data_conflicts field for details."
                )

            print(f"[CONTACTS] Returning successful response: {response_data}")
            return jsonify(response_data), 200

        except Exception as e:
            print(f"[CONTACTS] ERROR: Exception occurred during load_defaulters: {str(e)}")
            import traceback
            print(f"[CONTACTS] ERROR: Traceback: {traceback.format_exc()}")
            return jsonify({
                "status": "error",
                "message": f"Failed to process contacts: {str(e)}"
            }), 500
