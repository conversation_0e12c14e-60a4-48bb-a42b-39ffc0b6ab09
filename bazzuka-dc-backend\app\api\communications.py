import os
import json

from flask import Blueprint, jsonify, request, g

from app.utils.supabase.queries import get_comm_logs_by_defaulter_id

from app.core.defaulters import defaulters
from app.core.vapi import get_call_transcripts
from app.utils.supabase.queries import (
    get_comm_logs,
    get_emails_by_id,
    get_comm_by_id,
    get_most_recent_action_item_for_issue,
    update_comm_log,
    insert_comm_logs,
    get_calllog_by_id,
)

communications_bp = Blueprint("commmunications", __name__)


@communications_bp.route("", methods=["GET", "OPTIONS", "POST"])
def get_communications():
    if request.method == "OPTIONS":
        return jsonify({}), 204

    if os.environ.get("FLASK_DEBUG"):
        org_id = "8241d390-a8b5-4f59-b0a9-b95c074db3f5"
    else:
        try:
            org_id = g.user.user_metadata.get("org_id")
        except Exception as e:
            return jsonify({}), 500

    if request.method == "GET":
        # get the limit and offset from the query string
        limit = min(int(request.args.get("limit", 10)), 1000)
        offset = int(request.args.get("offset", 0))

        comm_logs = get_comm_logs(limit=limit, offset=offset)

        # Extract unique defaulter_ids from comm_logs
        defaulter_ids = {comm_log["defaulter_id"] for comm_log in comm_logs}

        # Batch fetch defaulters and create a lookup dictionary
        defaulters_map = {
            defaulter_id: defaulter.get("name")
            for defaulter_id, defaulter in zip(
                defaulter_ids, defaulters.get_defaulters_by_ids(defaulter_ids)
            )
            if defaulter
        }

        for comm_log in comm_logs:
            # Use batch-fetched defaulter data
            comm_log["name"] = defaulters_map.get(comm_log["defaulter_id"], "Unknown")

            # Use the commlog's own payment likelihood
            comm_log["payment_likelihood"] = comm_log.get("payment_likelihood", 0)

            comm_log.pop("issues", None)
            # comm_log.pop("id", None)

        return jsonify({"data": comm_logs}), 200

    elif request.method == "POST":
        data = request.get_json()
        if not data["id"]:
            # TODO: If posting WITHOUT an ID, then we are CREATING
            return (
                jsonify(
                    {
                        "error": "MissingAttributeError: A valid id is required to update this resource."
                    }
                ),
                400,
            )

        # TODO: Update the comm_log
        print(f"Updating comm log {data['id']} with {data}")
        feedback = data.get("feedback", None)

        update_comm_log(data["id"], {"feedback": feedback})
        return jsonify({"status": "success"}), 200


@communications_bp.route("/<comm_id>", methods=["GET", "OPTIONS"])
def get_communication(comm_id):
    # for production, assert UUID:
    # assert(len(defaulter_id) == 36)

    if request.method == "OPTIONS":
        return jsonify({}), 204

    if request.method == "GET":
        comm = get_comm_by_id(comm_id).data[0]
        summary = comm["summary"]
        is_manual = comm.get("is_manual", False)

        if comm["channel"] == "email":
            email = get_emails_by_id([comm_id]).data[0]
            return jsonify({
                "transcript": email["email_body"],
                "summary": summary,
                "is_manual": is_manual
            }), 200
        elif comm["channel"] == "call":
            transcript = get_call_transcripts(
                [{"comm_id": comm_id, "direction": "inbound"}]
            )[0]

            # Fetch audio recording URL from calllogs table
            audio_recording_url = None
            try:
                calllog = get_calllog_by_id(comm_id)
                if calllog.data:
                    audio_recording_url = calllog.data.get("audio_recording_url")
            except Exception as e:
                print(f"Error fetching calllog for {comm_id}: {e}")

            return (
                jsonify({
                    "transcript": transcript["transcript"],
                    "summary": summary,
                    "audio_recording_url": audio_recording_url,
                    "is_manual": is_manual
                }),
                200,
            )

    return jsonify({}), 404


@communications_bp.route("/user/<defaulter_id>", methods=["GET", "OPTIONS"])
def get_comm_history_by_defaulter_id(defaulter_id):
    if request.method == "OPTIONS":
        return jsonify({}), 204

    comm_history = get_comm_logs_by_defaulter_id(defaulter_id).data

    emails = []
    calls = []

    for comm in comm_history:
        if comm["channel"] == "email":
            emails.append(comm)
        elif comm["channel"] == "call":
            calls.append(comm)

    email_ids = [email["comm_id"] for email in emails]

    emails = get_emails_by_id(email_ids).data
    call_transcripts = get_call_transcripts(calls)

    # add the transcripts to the calls
    for call, transcript in zip(calls, call_transcripts):
        call["transcript"] = transcript["transcript"]

    for email in emails:
        email["transcript"] = email["email_body"]

    comm_history = calls + emails
    comm_history.sort(key=lambda x: x["timestamp"])

    return jsonify({"data": comm_history}), 200


@communications_bp.route("/manual", methods=["POST", "OPTIONS"])
def manual_communication():
    """Handle manual conversation logging from the frontend"""
    if request.method == "OPTIONS":
        return jsonify({"methods": ["POST", "OPTIONS"]}), 200

    try:
        data = request.get_json()
        print(f"[MANUAL_COMM] Received data: {data}")

        # Validate required fields
        required_fields = ["defaulter_id", "summary"]
        for field in required_fields:
            if not data.get(field):
                return jsonify({"error": f"Missing required field: {field}"}), 400

        defaulter_id = data["defaulter_id"]
        summary = data["summary"]
        channel = data.get("channel", "manual")  # Default to "manual" channel
        direction = data.get("direction", "inbound")  # Default to inbound

        # Validate defaulter exists
        defaulter = defaulters.get_defaulter_by_id(defaulter_id)
        if not defaulter:
            return jsonify({"error": f"Defaulter with ID {defaulter_id} not found"}), 404

        # Generate a unique comm_id for manual communications
        import uuid
        comm_id = str(uuid.uuid4())

        # Insert the manual communication log
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        result = insert_comm_logs(
            defaulter_id=defaulter_id,
            channel=channel,
            comm_id=comm_id,
            direction=direction,
            timestamp=timestamp,
            summary=summary,
            payment_likelihood=data.get("payment_likelihood", 0)
        )

        print(f"[MANUAL_COMM] Successfully logged manual communication: {result}")

        return jsonify({
            "status": "success",
            "message": "Manual conversation logged successfully",
            "comm_id": comm_id
        }), 200

    except Exception as e:
        print(f"[MANUAL_COMM] Error logging manual communication: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Failed to log manual communication: {str(e)}"}), 500
