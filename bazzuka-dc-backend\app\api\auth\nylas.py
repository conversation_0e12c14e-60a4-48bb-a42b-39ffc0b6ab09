import os
import time

from flask import (
    Blueprint,
    request,
    request,
    redirect,
    jsonify,
    g,
)
from nylas import Client
from nylas.models.auth import URLForAuthenticationConfig
from nylas.models.auth import CodeExchangeRequest

from app.utils.supabase.client import supabase as s
from app.utils.supabase.admin import update_user_metadata
from app.utils.supabase.queries import get_org_by_id, upsert_org_metadata

nylas_bp = Blueprint("nylas", __name__)
ROOT_DOMAIN = os.environ.get("ROOT_DOMAIN", "http://localhost:5000")

nylas = Client(
    api_key=os.environ.get("NYLAS_API_KEY"), api_uri=os.environ.get("NYLAS_API_URI")
)


@nylas_bp.route("/callback", methods=["GET"])
def authorized():
    try:
        code = request.args.get("code")
        exchangeRequest = CodeExchangeRequest(
            {
                "redirect_uri": ROOT_DOMAIN + "/v0/auth/nylas/callback",
                "code": code,
                "client_id": os.environ.get("NYLAS_CLIENT_ID"),
            }
        )
        exchange = nylas.auth.exchange_code_for_token(exchangeRequest)

        update_user_metadata(g.user, {"nylas_grant_id": exchange.grant_id})

        return redirect("http://localhost:3000/")

    except Exception as e:
        print(e)
        return (
            jsonify({"error": "Failed to exchange authorization code for token"}),
            500,
        )


# NOTE: This has changed and so the frontend code should be updated accordingly.
@nylas_bp.route("/", methods=["POST", "OPTIONS", "GET"])
def authorize():
    if g.user.user_metadata.get("nylas_grant_id"):
        return jsonify({"message": "success"}), 200

    config = URLForAuthenticationConfig(
        {
            "client_id": os.environ.get("NYLAS_CLIENT_ID"),
            "redirect_uri": ROOT_DOMAIN + "/v0/auth/nylas/callback",
        }
    )
    url = nylas.auth.url_for_oauth2(config)
    return redirect(url)


# Mailbox management endpoints for organization-level email integration
@nylas_bp.route("/mailbox/link", methods=["POST", "OPTIONS"])
def link_mailbox():
    """Initiate OAuth flow to link organization mailbox"""
    if request.method == "OPTIONS":
        return jsonify({}), 204

    try:
        # Get organization ID from request body
        data = request.get_json()
        org_id = data.get("org_id")

        if not org_id:
            return jsonify({"error": "Organization ID is required"}), 400

        # Check if organization already has a linked mailbox
        org = get_org_by_id(org_id).data[0]
        org_metadata = org.get("metadata", {})

        if org_metadata.get("nylas_grant_id"):
            return jsonify({"error": "Organization already has a linked mailbox"}), 400

        # Create OAuth URL with organization-specific callback
        config = URLForAuthenticationConfig(
            {
                "client_id": os.environ.get("NYLAS_CLIENT_ID"),
                "redirect_uri": ROOT_DOMAIN + f"/v0/auth/nylas/mailbox/callback?org_id={org_id}",
            }
        )
        url = nylas.auth.url_for_oauth2(config)

        return jsonify({"auth_url": url}), 200

    except Exception as e:
        print(f"Error initiating mailbox link: {e}")
        return jsonify({"error": "Failed to initiate mailbox linking"}), 500


@nylas_bp.route("/mailbox/callback", methods=["GET"])
def mailbox_callback():
    """Handle OAuth callback for organization mailbox linking"""
    try:
        code = request.args.get("code")
        org_id = request.args.get("org_id")

        if not code:
            return jsonify({"error": "No authorization code returned from Nylas"}), 400

        if not org_id:
            return jsonify({"error": "No organization ID provided"}), 400

        # Exchange code for token
        exchangeRequest = CodeExchangeRequest(
            {
                "redirect_uri": ROOT_DOMAIN + f"/v0/auth/nylas/mailbox/callback?org_id={org_id}",
                "code": code,
                "client_id": os.environ.get("NYLAS_CLIENT_ID"),
            }
        )
        exchange = nylas.auth.exchange_code_for_token(exchangeRequest)

        # Get grant details to store email address
        grant_details = nylas.auth.grants.find(exchange.grant_id)

        # Update organization metadata with grant ID and email
        org = get_org_by_id(org_id).data[0]
        org_metadata = org.get("metadata", {})

        org_metadata["nylas_grant_id"] = exchange.grant_id
        org_metadata["connected_email"] = grant_details.email
        org_metadata["mailbox_connected_at"] = int(time.time())

        upsert_org_metadata(org_id, org_metadata)

        # Redirect to settings page with success message
        return redirect("http://localhost:3000/settings?mailbox_linked=true")

    except Exception as e:
        print(f"Error in mailbox callback: {e}")
        return redirect("http://localhost:3000/settings?mailbox_error=true")


@nylas_bp.route("/mailbox/unlink", methods=["DELETE", "OPTIONS"])
def unlink_mailbox():
    """Unlink organization mailbox by deleting the grant"""
    if request.method == "OPTIONS":
        return jsonify({}), 204

    try:
        # Get organization ID from request body
        data = request.get_json()
        org_id = data.get("org_id")

        if not org_id:
            return jsonify({"error": "Organization ID is required"}), 400

        # Get current organization metadata
        org = get_org_by_id(org_id).data[0]
        org_metadata = org.get("metadata", {})

        grant_id = org_metadata.get("nylas_grant_id")
        if not grant_id:
            return jsonify({"error": "No mailbox is currently linked"}), 400

        # Delete the grant from Nylas
        nylas.auth.grants.destroy(grant_id)

        # Remove mailbox data from organization metadata
        if "nylas_grant_id" in org_metadata:
            del org_metadata["nylas_grant_id"]
        if "connected_email" in org_metadata:
            del org_metadata["connected_email"]
        if "mailbox_connected_at" in org_metadata:
            del org_metadata["mailbox_connected_at"]

        upsert_org_metadata(org_id, org_metadata)

        return jsonify({"message": "Mailbox unlinked successfully"}), 200

    except Exception as e:
        print(f"Error unlinking mailbox: {e}")
        return jsonify({"error": "Failed to unlink mailbox"}), 500


@nylas_bp.route("/mailbox/status", methods=["GET", "OPTIONS"])
def mailbox_status():
    """Get current mailbox connection status for organization"""
    if request.method == "OPTIONS":
        return jsonify({}), 204

    try:
        org_id = request.args.get("org_id")

        if not org_id:
            return jsonify({"error": "Organization ID is required"}), 400

        # Get organization metadata
        org = get_org_by_id(org_id).data[0]
        org_metadata = org.get("metadata", {})

        grant_id = org_metadata.get("nylas_grant_id")
        connected_email = org_metadata.get("connected_email")
        connected_at = org_metadata.get("mailbox_connected_at")

        if not grant_id:
            return jsonify({
                "connected": False,
                "email": None,
                "connected_at": None
            }), 200

        # Check if grant is still valid
        try:
            grant_details = nylas.auth.grants.find(grant_id)
            grant_valid = grant_details.grant_status == "valid"
        except:
            grant_valid = False

        return jsonify({
            "connected": grant_valid,
            "email": connected_email if grant_valid else None,
            "connected_at": connected_at if grant_valid else None,
            "grant_id": grant_id if grant_valid else None
        }), 200

    except Exception as e:
        print(f"Error checking mailbox status: {e}")
        return jsonify({"error": "Failed to check mailbox status"}), 500
