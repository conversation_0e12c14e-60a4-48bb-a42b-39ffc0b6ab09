import os
import time

from flask import Blueprint, jsonify, g, request, redirect
from nylas import Client
from nylas.models.auth import URLForAuthenticationConfig, CodeExchangeRequest

from ..utils.supabase.queries import get_org_by_id, upsert_org_metadata
from ..core.localstorage import inputs_storage, email_inputs_storage

orgs_bp = Blueprint("orgs", __name__)

# Initialize Nylas client
nylas = Client(
    api_key=os.environ.get("NYLAS_API_KEY"),
    api_uri=os.environ.get("NYLAS_API_URI", "https://api.us.nylas.com")
)
ROOT_DOMAIN = os.environ.get("ROOT_DOMAIN", "https://sf74vrgj-5000.inc1.devtunnels.ms")


@orgs_bp.route("/<org_id>", methods=["GET", "POST", "OPTIONS"])
def orgs(org_id=None):
    if request.method == "OPTIONS":
        return jsonify({}), 204

    org_id == "8241d390-a8b5-4f59-b0a9-b95c074db3f5"

    if request.method == "GET":
        org = get_org_by_id(org_id).data[0]
        return jsonify({"data": org}), 200

    if request.method == "POST":
        org_metadata = get_org_by_id(org_id).data[0].get("metadata")
        if not org_metadata:
            org_metadata = {}

        data = request.get_json()
        new_metadata = data.get("metadata")

        # Store old verification types for comparison
        old_verification_types = org_metadata.get("verification_types", [])
        old_fields = {vt["field"] for vt in old_verification_types}

        # Update metadata
        if new_metadata.get("type"):
            org_metadata["type"] = new_metadata["type"]
        if new_metadata.get("strategy"):
            org_metadata["strategy"] = new_metadata["strategy"]
        if new_metadata.get("settlement_plans"):
            org_metadata["settlement_plans"] = new_metadata["settlement_plans"]
        if new_metadata.get("promise_failure_policy"):
            org_metadata["promise_failure_policy"] = new_metadata[
                "promise_failure_policy"
            ]
        if new_metadata.get("call_forwarding"):
            org_metadata["call_forwarding"] = new_metadata["call_forwarding"]
        if new_metadata.get("communication_channels"):
            org_metadata["communication_channels"] = new_metadata[
                "communication_channels"
            ]

        if new_metadata.get("verification_types"):
            print(new_metadata["verification_types"])
            org_metadata["verification_types"] = new_metadata["verification_types"]
        else:
            org_metadata["verification_types"] = []

        # Handle mailbox-related metadata
        if new_metadata.get("nylas_grant_id"):
            org_metadata["nylas_grant_id"] = new_metadata["nylas_grant_id"]
        if new_metadata.get("connected_email"):
            org_metadata["connected_email"] = new_metadata["connected_email"]
        if new_metadata.get("mailbox_connected_at"):
            org_metadata["mailbox_connected_at"] = new_metadata["mailbox_connected_at"]

        try:
            upsert_org_metadata(org_id, org_metadata)
            return jsonify({"data": {"metadata": org_metadata}}), 200
        except Exception as e:
            return jsonify({"error": "Could not update this org"}), 500


@orgs_bp.route("/<org_id>/mailbox/status", methods=["GET", "OPTIONS"])
def get_mailbox_status(org_id):
    """Get current mailbox connection status for organization"""
    if request.method == "OPTIONS":
        return jsonify({}), 204

    try:
        # Get organization metadata
        org = get_org_by_id(org_id).data[0]
        org_metadata = org.get("metadata", {})

        grant_id = org_metadata.get("nylas_grant_id")
        connected_email = org_metadata.get("connected_email")
        connected_at = org_metadata.get("mailbox_connected_at")

        if not grant_id:
            return jsonify({
                "connected": False,
                "email": None,
                "connected_at": None
            }), 200

        # For now, assume grant is valid if it exists
        # In production, you might want to verify with Nylas API
        return jsonify({
            "connected": True,
            "email": connected_email,
            "connected_at": connected_at,
            "grant_id": grant_id
        }), 200

    except Exception as e:
        print(f"Error checking mailbox status: {e}")
        return jsonify({"error": "Failed to check mailbox status"}), 500


@orgs_bp.route("/<org_id>/mailbox/link", methods=["POST", "OPTIONS"])
def link_mailbox(org_id):
    """Initiate OAuth flow to link organization mailbox"""
    if request.method == "OPTIONS":
        return jsonify({}), 204

    try:
        # Check if organization already has a linked mailbox
        org = get_org_by_id(org_id).data[0]
        org_metadata = org.get("metadata", {})

        if org_metadata.get("nylas_grant_id"):
            return jsonify({"error": "Organization already has a linked mailbox"}), 400

        # Create OAuth URL with organization-specific callback
        config = URLForAuthenticationConfig(
            {
                "client_id": os.environ.get("NYLAS_CLIENT_ID"),
                "redirect_uri": ROOT_DOMAIN + f"/v0/orgs/{org_id}/mailbox/callback",
            }
        )
        url = nylas.auth.url_for_oauth2(config)

        return jsonify({"auth_url": url}), 200

    except Exception as e:
        print(f"Error initiating mailbox link: {e}")
        return jsonify({"error": "Failed to initiate mailbox linking"}), 500


@orgs_bp.route("/<org_id>/mailbox/callback", methods=["GET"])
def mailbox_callback(org_id):
    """Handle OAuth callback for organization mailbox linking"""
    try:
        code = request.args.get("code")

        if not code:
            return jsonify({"error": "No authorization code returned from Nylas"}), 400

        # Exchange code for token
        exchangeRequest = CodeExchangeRequest(
            {
                "redirect_uri": ROOT_DOMAIN + f"/v0/orgs/{org_id}/mailbox/callback",
                "code": code,
                "client_id": os.environ.get("NYLAS_CLIENT_ID"),
            }
        )
        exchange = nylas.auth.exchange_code_for_token(exchangeRequest)

        # Get grant details to store email address
        grant_details = nylas.auth.grants.find(exchange.grant_id)

        # Update organization metadata with grant ID and email
        org = get_org_by_id(org_id).data[0]
        org_metadata = org.get("metadata", {})

        org_metadata["nylas_grant_id"] = exchange.grant_id
        org_metadata["connected_email"] = grant_details.email
        org_metadata["mailbox_connected_at"] = int(time.time())

        upsert_org_metadata(org_id, org_metadata)

        # Redirect to settings page with success message
        return redirect("http://localhost:3000/settings?mailbox_linked=true")

    except Exception as e:
        print(f"Error in mailbox callback: {e}")
        return redirect("http://localhost:3000/settings?mailbox_error=true")


@orgs_bp.route("/<org_id>/mailbox/unlink", methods=["DELETE", "OPTIONS"])
def unlink_mailbox(org_id):
    """Unlink organization mailbox by deleting the grant"""
    if request.method == "OPTIONS":
        return jsonify({}), 204

    try:
        # Get current organization metadata
        org = get_org_by_id(org_id).data[0]
        org_metadata = org.get("metadata", {})

        grant_id = org_metadata.get("nylas_grant_id")
        if not grant_id:
            return jsonify({"error": "No mailbox is currently linked"}), 400

        # Delete the grant from Nylas
        nylas.auth.grants.destroy(grant_id)

        # Remove mailbox data from organization metadata
        if "nylas_grant_id" in org_metadata:
            del org_metadata["nylas_grant_id"]
        if "connected_email" in org_metadata:
            del org_metadata["connected_email"]
        if "mailbox_connected_at" in org_metadata:
            del org_metadata["mailbox_connected_at"]

        upsert_org_metadata(org_id, org_metadata)

        return jsonify({"message": "Mailbox unlinked successfully"}), 200

    except Exception as e:
        print(f"Error unlinking mailbox: {e}")
        return jsonify({"error": "Failed to unlink mailbox"}), 500
