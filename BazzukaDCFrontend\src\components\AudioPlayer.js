import React, { useState, useRef, useEffect } from 'react';
import { CButton } from '@coreui/react';

const AudioPlayer = ({ audioUrl }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const audioRef = useRef(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleEnded = () => setIsPlaying(false);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('loadstart', handleLoadStart);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioUrl]);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e) => {
    const audio = audioRef.current;
    if (!audio) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (time) => {
    if (isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (!audioUrl) {
    return null;
  }

  return (
    <div className="audio-player" style={{
      border: '1px solid #dee2e6',
      borderRadius: '8px',
      padding: '16px',
      backgroundColor: '#ffffff',
      boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
      width: '100%',
      maxWidth: '500px'
    }}>
      <div className="mb-3">
        <small className="text-muted fw-medium">Listen to call recording</small>
      </div>
      <audio ref={audioRef} src={audioUrl} preload="metadata" />

      <div className="d-flex align-items-center gap-3">
        <CButton
          color="primary"
          variant="outline"
          size="sm"
          onClick={togglePlayPause}
          disabled={isLoading}
          style={{
            minWidth: '70px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px'
          }}
        >
          {isLoading ? '...' : isPlaying ? '⏸️' : '▶️'}
        </CButton>

        <div className="flex-grow-1">
          <div
            className="progress-bar-container"
            style={{
              height: '8px',
              backgroundColor: '#e9ecef',
              borderRadius: '4px',
              cursor: 'pointer',
              position: 'relative',
              marginBottom: '8px'
            }}
            onClick={handleSeek}
          >
            <div
              className="progress-bar"
              style={{
                height: '100%',
                backgroundColor: '#0d6efd',
                borderRadius: '4px',
                width: duration ? `${(currentTime / duration) * 100}%` : '0%',
                transition: 'width 0.1s ease'
              }}
            />
          </div>

          <div className="d-flex justify-content-between align-items-center">
            <small className="text-muted fw-medium">{formatTime(currentTime)}</small>
            <small className="text-muted fw-medium">{formatTime(duration)}</small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
