import React, { useState, useEffect } from "react";
import {
  <PERSON>orm,
  <PERSON>orm<PERSON><PERSON>ck,
  CFormLabel,
  CFormTextarea,
  CButton,
  CFormSelect,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CModalFooter,
} from "@coreui/react";
import { useTimezone, COMMON_TIMEZONES } from "../../timezoneContext";
import { getApiUrl } from '../../utils/apiConfig'

const FormPage = () => {
  const org_id = sessionStorage.getItem("org_id");
  const { timezone, updateTimezone } = useTimezone();

  const [formData, setFormData] = useState({
    isFinanceCompany: null,
    question2: "",
    question3: "",
    question4: "",
    question5: "",
    question6: "",
    communicationChannels: {
      email: false,
      calls: false,
      texts: false
    }
  });

  // Mailbox connection state
  const [mailboxStatus, setMailboxStatus] = useState({
    connected: false,
    email: null,
    connected_at: null,
    loading: true
  });
  const [mailboxLoading, setMailboxLoading] = useState(false);

  const [callForwardingFields, setCallForwardingFields] = useState([]);
  const [verificationTypes, setVerificationTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [validationErrors, setValidationErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationModalMessage, setValidationModalMessage] = useState('');

  // Notification states
  const [notificationPermission, setNotificationPermission] = useState(Notification.permission);
  const [notificationsEnabled, setNotificationsEnabled] = useState(() => {
    // Get the saved preference from localStorage, default to false
    const saved = localStorage.getItem('notificationsEnabled');
    return saved !== null ? JSON.parse(saved) : false;
  });

  // Fetch mailbox status
  const fetchMailboxStatus = async () => {
    try {
      const response = await fetch(getApiUrl(`/v0/orgs/${org_id}/mailbox/status`), {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "ngrok-skip-browser-warning": "true",
          "Access-Control-Allow-Origin": "*",
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setMailboxStatus({
          ...data,
          loading: false
        });
      } else {
        setMailboxStatus(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error("Error fetching mailbox status:", error);
      setMailboxStatus(prev => ({ ...prev, loading: false }));
    }
  };

  // Link mailbox
  const handleLinkMailbox = async () => {
    setMailboxLoading(true);
    try {
      const response = await fetch(getApiUrl(`/v0/orgs/${org_id}/mailbox/link`), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "ngrok-skip-browser-warning": "true",
          "Access-Control-Allow-Origin": "*",
        },
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        // Redirect to Nylas OAuth
        window.location.href = data.auth_url;
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Failed to initiate mailbox linking");
        setMailboxLoading(false);
      }
    } catch (error) {
      console.error("Error linking mailbox:", error);
      setError("Failed to link mailbox");
      setMailboxLoading(false);
    }
  };

  // Unlink mailbox
  const handleUnlinkMailbox = async () => {
    if (!window.confirm("Are you sure you want to unlink your mailbox? This will stop email integration.")) {
      return;
    }

    setMailboxLoading(true);
    try {
      const response = await fetch(getApiUrl(`/v0/orgs/${org_id}/mailbox/unlink`), {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          "ngrok-skip-browser-warning": "true",
          "Access-Control-Allow-Origin": "*",
        },
        credentials: 'include',
      });

      if (response.ok) {
        setError(null);
        setMailboxStatus({
          connected: false,
          email: null,
          connected_at: null,
          loading: false
        });
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Failed to unlink mailbox");
      }
    } catch (error) {
      console.error("Error unlinking mailbox:", error);
      setError("Failed to unlink mailbox");
    }
    setMailboxLoading(false);
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Clear validation error for this field when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }

    if (name.startsWith('channel_')) {
      const channel = name.replace('channel_', '');
      setFormData(prevData => ({
        ...prevData,
        communicationChannels: {
          ...prevData.communicationChannels,
          [channel]: checked
        }
      }));

      // Clear communication channels error when any channel is selected
      if (checked && validationErrors.communicationChannels) {
        setValidationErrors(prev => ({
          ...prev,
          communicationChannels: undefined
        }));
      }
    } else {
      setFormData((prevData) => ({
        ...prevData,
        [name]: type === "radio" ? value === "true" : value,
      }));
    }
  };

  const handleTimezoneChange = (e) => {
    updateTimezone(e.target.value);
  };

  const handleNotificationToggle = async () => {
    if (!notificationsEnabled) {
      // User wants to enable notifications
      if (notificationPermission === 'default') {
        // Request permission
        const permission = await Notification.requestPermission();
        setNotificationPermission(permission);

        if (permission === 'granted') {
          setNotificationsEnabled(true);
          localStorage.setItem('notificationsEnabled', 'true');

          // Show a test notification
          new Notification('Notifications Enabled', {
            body: 'You will now receive browser notifications for pending approvals.',
            icon: '/favicon.ico'
          });
        } else {
          alert('Notification permission denied. Please enable notifications in your browser settings.');
        }
      } else if (notificationPermission === 'granted') {
        // Permission already granted, just enable
        setNotificationsEnabled(true);
        localStorage.setItem('notificationsEnabled', 'true');

        // Show a test notification
        new Notification('Notifications Enabled', {
          body: 'You will now receive browser notifications for pending approvals.',
          icon: '/favicon.ico'
        });
      } else {
        // Permission denied
        alert('Notification permission denied. Please enable notifications in your browser settings and refresh the page.');
      }
    } else {
      // User wants to disable notifications
      setNotificationsEnabled(false);
      localStorage.setItem('notificationsEnabled', 'false');
    }
  };

  const addCallForwardingField = () => {
    setCallForwardingFields([...callForwardingFields, { name: "", number: "", purpose: "", message: "" }]);
  };

  const handleCallForwardingChange = (index, e) => {
    const { name, value } = e.target;
    const updatedFields = callForwardingFields.map((field, i) =>
      i === index ? { ...field, [name]: value } : field
    );
    setCallForwardingFields(updatedFields);

    // Clear validation error for this specific field
    const errorKey = `${name}_${index}`;
    if (validationErrors[errorKey]) {
      setValidationErrors(prev => ({
        ...prev,
        [errorKey]: undefined
      }));
    }
  };

  const deleteCallForwardingField = (index) => {
    setCallForwardingFields(callForwardingFields.filter((_, i) => i !== index));
  };

  const addVerificationType = () => {
    setVerificationTypes([...verificationTypes, { name: "", field: "" }]);
  };

  const handleVerificationTypeChange = (index, e) => {
    const { name, value } = e.target;
    const updatedTypes = verificationTypes.map((type, i) =>
      i === index ? { ...type, [name]: value } : type
    );
    setVerificationTypes(updatedTypes);

    // Clear validation error for this specific field
    const errorKey = `${name}_${index}`;
    if (validationErrors[errorKey]) {
      setValidationErrors(prev => ({
        ...prev,
        [errorKey]: undefined
      }));
    }
  };

  const deleteVerificationType = (index) => {
    setVerificationTypes(verificationTypes.filter((_, i) => i !== index));
  };

  const validateVerificationTypes = () => {
    return verificationTypes.every(type =>
      type.name.trim() !== "" && type.field.trim() !== ""
    );
  };

  // Comprehensive validation function
  const validateForm = () => {
    const errors = {};

    // Company Type validation
    if (formData.isFinanceCompany === null) {
      errors.isFinanceCompany = "Please select whether you are a finance company";
    }

    // Communication Channels validation
    if (!Object.values(formData.communicationChannels).some(channel => channel)) {
      errors.communicationChannels = "Please select at least one communication channel";
    }

    // Collection Strategies validation
    if (!formData.question2.trim()) {
      errors.question2 = "Collections strategy is required";
    } else if (formData.question2.trim().length < 10) {
      errors.question2 = "Collections strategy must be at least 10 characters";
    }

    if (!formData.question3.trim()) {
      errors.question3 = "Settlement plans description is required";
    } else if (formData.question3.trim().length < 10) {
      errors.question3 = "Settlement plans description must be at least 10 characters";
    }

    // Channel-Specific Strategies validation
    if (formData.communicationChannels.email && !formData.question4.trim()) {
      errors.question4 = "Email strategy is required when email channel is selected";
    } else if (formData.question4.trim() && formData.question4.trim().length < 10) {
      errors.question4 = "Email strategy must be at least 10 characters";
    }

    if (formData.communicationChannels.calls && !formData.question5.trim()) {
      errors.question5 = "Calling strategy is required when phone calls channel is selected";
    } else if (formData.question5.trim() && formData.question5.trim().length < 10) {
      errors.question5 = "Calling strategy must be at least 10 characters";
    }

    if (formData.communicationChannels.texts && !formData.question6.trim()) {
      errors.question6 = "Texting strategy is required when text messages channel is selected";
    } else if (formData.question6.trim() && formData.question6.trim().length < 10) {
      errors.question6 = "Texting strategy must be at least 10 characters";
    }

    // Call Forwarding validation
    const callForwardingErrors = {};
    callForwardingFields.forEach((field, index) => {
      if (field.name.trim() || field.number.trim() || field.purpose.trim() || field.message.trim()) {
        // If any field is filled, all required fields must be filled
        if (!field.name.trim()) {
          callForwardingErrors[`name_${index}`] = "Name is required";
        }
        if (!field.number.trim()) {
          callForwardingErrors[`number_${index}`] = "Phone number is required";
        } else if (!/^\+?[\d\s\-\(\)]+$/.test(field.number.trim())) {
          callForwardingErrors[`number_${index}`] = "Please enter a valid phone number";
        }
        if (!field.purpose.trim()) {
          callForwardingErrors[`purpose_${index}`] = "Purpose is required";
        }
        if (!field.message.trim()) {
          callForwardingErrors[`message_${index}`] = "Transfer message is required";
        }
      }
    });

    // Verification Types validation
    const verificationErrors = {};
    verificationTypes.forEach((type, index) => {
      if (type.name.trim() || type.field.trim()) {
        // If any field is filled, both must be filled
        if (!type.name.trim()) {
          verificationErrors[`name_${index}`] = "Display name is required";
        }
        if (!type.field.trim()) {
          verificationErrors[`field_${index}`] = "Field name is required";
        } else if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(type.field.trim())) {
          verificationErrors[`field_${index}`] = "Field name must be a valid identifier (letters, numbers, underscore)";
        }
      }
    });

    // Combine all errors
    const allErrors = {
      ...errors,
      ...callForwardingErrors,
      ...verificationErrors
    };

    return allErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    // Run comprehensive validation
    const errors = validateForm();
    setValidationErrors(errors);

    // If there are validation errors, show detailed dialog and stop submission
    if (Object.keys(errors).length > 0) {
      const errorMessages = [];

      // Group errors by section
      if (errors.isFinanceCompany) {
        errorMessages.push("• Company Type: Please select whether you are a finance company");
      }

      if (errors.communicationChannels) {
        errorMessages.push("• Communication Channels: Please select at least one channel");
      }

      if (errors.question2) {
        errorMessages.push("• Collections Strategy: " + errors.question2);
      }

      if (errors.question3) {
        errorMessages.push("• Settlement Plans: " + errors.question3);
      }

      if (errors.question4) {
        errorMessages.push("• Email Strategy: " + errors.question4);
      }

      if (errors.question5) {
        errorMessages.push("• Calling Strategy: " + errors.question5);
      }

      if (errors.question6) {
        errorMessages.push("• Texting Strategy: " + errors.question6);
      }

      // Call forwarding errors
      const callForwardingErrors = Object.keys(errors).filter(key =>
        key.includes('name_') || key.includes('number_') || key.includes('purpose_') || key.includes('message_')
      );

      if (callForwardingErrors.length > 0) {
        errorMessages.push("• Call Forwarding: Please complete all required fields for call forwarding entries");
        callForwardingErrors.forEach(key => {
          const [field, index] = key.split('_');
          const fieldName = field.charAt(0).toUpperCase() + field.slice(1);
          errorMessages.push(`  - Row ${parseInt(index) + 1}: ${fieldName} is required`);
        });
      }

      // Verification types errors
      const verificationErrors = Object.keys(errors).filter(key =>
        key.includes('field_') && !key.includes('name_') && !key.includes('number_') && !key.includes('purpose_') && !key.includes('message_')
      );

      if (verificationErrors.length > 0) {
        errorMessages.push("• Verification Types: Please complete all required fields for verification type entries");
      }

      const dialogMessage = "Please fix the following validation errors before saving:\n\n" + errorMessages.join('\n');

      setValidationModalMessage(dialogMessage);
      setShowValidationModal(true);
      setError("Please fix the validation errors below before saving");
      setIsSubmitting(false);
      return;
    }

    // Transform the formData into the required structure
    const apiBody = {
      metadata: {
        type: formData.isFinanceCompany ? "finance" : "other",
        strategy: formData.question2,
        settlement_plans: formData.question3,
        email_strategy: formData.question4,
        calling_strategy: formData.question5,
        text_strategy: formData.question6,
        call_forwarding: callForwardingFields,
        verification_types: verificationTypes,
        communication_channels: formData.communicationChannels
      },
    };

    try {
      const response = await fetch(getApiUrl(`/v0/orgs/${org_id}`), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "ngrok-skip-browser-warning": "true",
          "Access-Control-Allow-Origin": "*",
        },
        body: JSON.stringify(apiBody),
      });

      const responseData = await response.json();

      if (response.ok) {
        // Update local state with the response data to ensure consistency
        if (responseData.data?.metadata) {
          const metadata = responseData.data.metadata;
          setFormData(prev => ({
            ...prev,
            isFinanceCompany: metadata.type === "finance",
            question2: metadata.strategy || "",
            question3: metadata.settlement_plans || "",
            question4: metadata.email_strategy || "",
            question5: metadata.calling_strategy || "",
            question6: metadata.text_strategy || "",
            communicationChannels: metadata.communication_channels || {
              email: false,
              calls: false,
              texts: false
            }
          }));
          setCallForwardingFields(metadata.call_forwarding || []);
          setVerificationTypes(metadata.verification_types || []);
        }
        alert("Settings saved successfully!");
        setValidationErrors({}); // Clear validation errors on successful save
      } else {
        setError(responseData.error || "Failed to save settings");
      }
    } catch (error) {
      setError("Error saving settings: " + error.message);
      console.error("Error submitting the form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchAnswers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(getApiUrl(`/v0/orgs/${org_id}`));
      const data = await response.json();

      if (response.ok && data.data?.metadata) {
        const metadata = data.data.metadata;

        // Update form data
        setFormData({
          isFinanceCompany: metadata.type === "finance",
          question2: metadata.strategy || "",
          question3: metadata.settlement_plans || "",
          question4: metadata.email_strategy || "",
          question5: metadata.calling_strategy || "",
          question6: metadata.text_strategy || "",
          communicationChannels: metadata.communication_channels || {
            email: false,
            calls: false,
            texts: false
          }
        });

        // Update call forwarding fields
        setCallForwardingFields(metadata.call_forwarding || []);

        // Update verification types
        setVerificationTypes(metadata.verification_types || []);
      } else {
        setError("Failed to fetch settings");
      }
    } catch (error) {
      setError("Error fetching settings: " + error.message);
      console.error("Error fetching answers:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnswers();
    fetchMailboxStatus();

    // Check for mailbox linking success/error from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('mailbox_linked') === 'true') {
      setError(null);
      // Refresh mailbox status after successful linking
      setTimeout(() => {
        fetchMailboxStatus();
      }, 1000);
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (urlParams.get('mailbox_error') === 'true') {
      setError("Failed to link mailbox. Please try again.");
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  if (isLoading) {
    return <div>Loading settings...</div>;
  }

  return (
    <div style={{ padding: "2rem" }}>
      <h2>Settings</h2>
      {error && (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      )}
      <CForm onSubmit={handleSubmit} className="mt-3">
        {/* Company Type Section */}
        <div className="mb-4">
          <div className="h4">Company Type</div>
          <div className="mb-3">
            <CFormLabel>Are you a finance company? <span className="text-danger">*</span></CFormLabel>
            <div>
              <CFormCheck
                type="radio"
                name="isFinanceCompany"
                id="financeYes"
                label="Yes"
                value="true"
                checked={formData.isFinanceCompany === true}
                onChange={handleChange}
              />
              <CFormCheck
                type="radio"
                name="isFinanceCompany"
                id="financeNo"
                label="No"
                value="false"
                checked={formData.isFinanceCompany === false}
                onChange={handleChange}
              />
            </div>
            {validationErrors.isFinanceCompany && (
              <div className="text-danger small mt-1">
                {validationErrors.isFinanceCompany}
              </div>
            )}
          </div>
        </div>

        {/* Communication Channels Section */}
        <div className="mb-4">
          <div className="h4">Communication Channels</div>
          <div className="mb-3">
            <CFormLabel>Select available communication channels: <span className="text-danger">*</span></CFormLabel>
            <div>
              <CFormCheck
                type="checkbox"
                name="channel_email"
                id="channelEmail"
                label="Email"
                checked={formData.communicationChannels.email}
                onChange={handleChange}
              />
              <CFormCheck
                type="checkbox"
                name="channel_calls"
                id="channelCalls"
                label="Phone Calls"
                checked={formData.communicationChannels.calls}
                onChange={handleChange}
              />
              <CFormCheck
                type="checkbox"
                name="channel_texts"
                id="channelTexts"
                label="Text Messages"
                checked={formData.communicationChannels.texts}
                onChange={handleChange}
              />
            </div>
            {validationErrors.communicationChannels && (
              <div className="text-danger small mt-1">
                {validationErrors.communicationChannels}
              </div>
            )}
          </div>
        </div>

        {/* Collection Strategies Section */}
        <div className="mb-4">
          <div className="h4">Collection Strategies</div>
          <div className="mb-3">
            <CFormLabel htmlFor="question2">Collections Strategy <span className="text-danger">*</span></CFormLabel>
            <CFormTextarea
              id="question2"
              name="question2"
              value={formData.question2}
              placeholder="Describe your overall collections strategy (minimum 10 characters)"
              onChange={handleChange}
              rows="4"
              invalid={!!validationErrors.question2}
            />
            {validationErrors.question2 && (
              <div className="text-danger small mt-1">
                {validationErrors.question2}
              </div>
            )}
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="question3">Settlement Plans <span className="text-danger">*</span></CFormLabel>
            <CFormTextarea
              id="question3"
              name="question3"
              value={formData.question3}
              placeholder="Describe your settlement plan policies and flexibility (minimum 10 characters)"
              onChange={handleChange}
              rows="4"
              invalid={!!validationErrors.question3}
            />
            {validationErrors.question3 && (
              <div className="text-danger small mt-1">
                {validationErrors.question3}
              </div>
            )}
          </div>
        </div>

        {/* Channel-Specific Strategies Section */}
        <div className="mb-4">
          <div className="h4">Channel-Specific Strategies</div>
          <div className="mb-3">
            <CFormLabel htmlFor="question4">
              Email Strategy
              {formData.communicationChannels.email && <span className="text-danger">*</span>}
            </CFormLabel>
            <CFormTextarea
              id="question4"
              name="question4"
              value={formData.question4}
              placeholder="Describe your email communication strategy (minimum 10 characters)"
              onChange={handleChange}
              rows="4"
              invalid={!!validationErrors.question4}
            />
            {validationErrors.question4 && (
              <div className="text-danger small mt-1">
                {validationErrors.question4}
              </div>
            )}
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="question5">
              Calling Strategy
              {formData.communicationChannels.calls && <span className="text-danger">*</span>}
            </CFormLabel>
            <CFormTextarea
              id="question5"
              name="question5"
              value={formData.question5}
              placeholder="Describe your phone call communication strategy (minimum 10 characters)"
              onChange={handleChange}
              rows="4"
              invalid={!!validationErrors.question5}
            />
            {validationErrors.question5 && (
              <div className="text-danger small mt-1">
                {validationErrors.question5}
              </div>
            )}
          </div>

          <div className="mb-3">
            <CFormLabel htmlFor="question6">
              Texting Strategy
              {formData.communicationChannels.texts && <span className="text-danger">*</span>}
            </CFormLabel>
            <CFormTextarea
              id="question6"
              name="question6"
              value={formData.question6}
              placeholder="Describe your text message communication strategy (minimum 10 characters)"
              onChange={handleChange}
              rows="4"
              invalid={!!validationErrors.question6}
            />
            {validationErrors.question6 && (
              <div className="text-danger small mt-1">
                {validationErrors.question6}
              </div>
            )}
          </div>
        </div>

        {/* Call Forwarding Section */}
        <div className="mb-4">
          <div className="h4">Call Forwarding</div>
          <p className="text-muted">
            Configure destinations for call transfers.
            <strong> If you fill any field in a row, all fields in that row become required.</strong>
          </p>
          {callForwardingFields.map((field, index) => (
            <div key={index} className="mb-3" style={{ display: "flex", gap: "1rem", alignItems: "center" }}>
              <div style={{ flex: 1 }}>
                <CFormLabel htmlFor={`name-${index}`}>Name</CFormLabel>
                <CFormTextarea
                  id={`name-${index}`}
                  name="name"
                  value={field.name}
                  onChange={(e) => handleCallForwardingChange(index, e)}
                  rows="1"
                  placeholder="Enter contact name"
                  invalid={!!validationErrors[`name_${index}`]}
                />
                {validationErrors[`name_${index}`] && (
                  <div className="text-danger small mt-1">
                    {validationErrors[`name_${index}`]}
                  </div>
                )}
              </div>
              <div style={{ flex: 1 }}>
                <CFormLabel htmlFor={`number-${index}`}>Number</CFormLabel>
                <CFormTextarea
                  id={`number-${index}`}
                  name="number"
                  value={field.number}
                  onChange={(e) => handleCallForwardingChange(index, e)}
                  rows="1"
                  placeholder="Enter phone number"
                  invalid={!!validationErrors[`number_${index}`]}
                />
                {validationErrors[`number_${index}`] && (
                  <div className="text-danger small mt-1">
                    {validationErrors[`number_${index}`]}
                  </div>
                )}
              </div>
              <div style={{ flex: 1 }}>
                <CFormLabel htmlFor={`purpose-${index}`}>Purpose</CFormLabel>
                <CFormTextarea
                  id={`purpose-${index}`}
                  name="purpose"
                  value={field.purpose}
                  onChange={(e) => handleCallForwardingChange(index, e)}
                  rows="1"
                  placeholder="Enter purpose"
                  invalid={!!validationErrors[`purpose_${index}`]}
                />
                {validationErrors[`purpose_${index}`] && (
                  <div className="text-danger small mt-1">
                    {validationErrors[`purpose_${index}`]}
                  </div>
                )}
              </div>
              <div style={{ flex: 1 }}>
                <CFormLabel htmlFor={`message-${index}`}>Message</CFormLabel>
                <CFormTextarea
                  id={`message-${index}`}
                  name="message"
                  value={field.message}
                  onChange={(e) => handleCallForwardingChange(index, e)}
                  rows="1"
                  placeholder="Enter transfer message"
                  invalid={!!validationErrors[`message_${index}`]}
                />
                {validationErrors[`message_${index}`] && (
                  <div className="text-danger small mt-1">
                    {validationErrors[`message_${index}`]}
                  </div>
                )}
              </div>
              <CButton color="danger" className="text-white p-0 w-6 h-6 flex items-center justify-center rounded-full" onClick={() => deleteCallForwardingField(index)}>
                x
              </CButton>
            </div>
          ))}
          <CButton onClick={addCallForwardingField} className="border border-primary text-primary bg-transparent p-2 rounded" >
            Add Call Forwarding
          </CButton>
        </div>

        {/* Verification Types Section */}
        <div className="mb-4">
          <div className="h4">Verification Types</div>
          <p className="text-muted">
            Configure the types of verification required for customer authentication.
            <strong> If you fill any field in a row, all fields in that row become required.</strong>
          </p>
          {verificationTypes.map((type, index) => (
            <div key={index} className="mb-3" style={{ display: "flex", gap: "1rem", alignItems: "center" }}>
              <div style={{ flex: 1 }}>
                <CFormLabel htmlFor={`verification-name-${index}`}>Display Name</CFormLabel>
                <CFormTextarea
                  id={`verification-name-${index}`}
                  name="name"
                  value={type.name}
                  onChange={(e) => handleVerificationTypeChange(index, e)}
                  rows="1"
                  placeholder="Enter verification type name"
                  invalid={!!validationErrors[`name_${index}`]}
                />
                {validationErrors[`name_${index}`] && (
                  <div className="text-danger small mt-1">
                    {validationErrors[`name_${index}`]}
                  </div>
                )}
              </div>
              <div style={{ flex: 1 }}>
                <CFormLabel htmlFor={`verification-field-${index}`}>Field Name</CFormLabel>
                <CFormTextarea
                  id={`verification-field-${index}`}
                  name="field"
                  value={type.field}
                  onChange={(e) => handleVerificationTypeChange(index, e)}
                  rows="1"
                  placeholder="Enter field name (e.g., ssn, dob)"
                  invalid={!!validationErrors[`field_${index}`]}
                />
                {validationErrors[`field_${index}`] && (
                  <div className="text-danger small mt-1">
                    {validationErrors[`field_${index}`]}
                  </div>
                )}
              </div>
              <CButton color="danger" className="text-white p-0 w-6 h-6 flex items-center justify-center rounded-full" onClick={() => deleteVerificationType(index)}>
                x
              </CButton>
            </div>
          ))}
          <CButton onClick={addVerificationType} className="border border-primary text-primary bg-transparent p-2 rounded" >
            Add Verification Type
          </CButton>
        </div>

         {/* Timezone Selection Section */}
        <div className="mb-4">
          <div className="h4">Timezone Settings</div>
          <div className="mb-3">
            <CFormLabel htmlFor="timezone-select">Select Timezone:</CFormLabel>
            <CFormSelect
              id="timezone-select"
              value={timezone}
              onChange={handleTimezoneChange}
              className="mb-3"
              aria-label="Timezone selection"
            >
              {COMMON_TIMEZONES.map((tz) => (
                <option key={tz.value} value={tz.value}>
                  {tz.label}
                </option>
              ))}
            </CFormSelect>
            <p className="text-muted">
              This setting affects how dates and times are displayed throughout the application.
            </p>
          </div>
        </div>

        {/* Notification Settings Section */}
        <div className="mb-4">
          <div className="h4">Notification Settings</div>
          <div className="mb-3">
            <CFormLabel>Browser Notifications:</CFormLabel>
            <div className="d-flex align-items-center gap-3">
              <CButton
                color={notificationsEnabled ? "success" : "secondary"}
                onClick={handleNotificationToggle}
                className="d-flex align-items-center gap-2"
              >
                <span>{notificationsEnabled ? "🔔" : "🔕"}</span>
                {notificationsEnabled ? "Enabled" : "Disabled"}
              </CButton>
              <div className="text-muted">
                {notificationPermission === 'denied' && (
                  <small className="text-danger">
                    ⚠️ Notifications blocked. Please enable in browser settings.
                  </small>
                )}
                {notificationPermission === 'default' && (
                  <small>
                    Click to enable browser notifications for pending approvals.
                  </small>
                )}
                {notificationPermission === 'granted' && notificationsEnabled && (
                  <small className="text-success">
                    ✅ You will receive notifications for pending approvals.
                  </small>
                )}
                {notificationPermission === 'granted' && !notificationsEnabled && (
                  <small>
                    Notifications are disabled. Click to enable.
                  </small>
                )}
              </div>
            </div>
            <p className="text-muted mt-2">
              When enabled, you'll receive browser notifications when new items require approval.
              This helps you stay updated even when the application is not in focus.
            </p>
          </div>
        </div>

        {/* Email Integration Section */}
        <div className="mb-4">
          <div className="h4">Email Integration</div>
          <div className="mb-3">
            <CFormLabel>Mailbox Connection:</CFormLabel>
            {mailboxStatus.loading ? (
              <div className="text-muted">Loading mailbox status...</div>
            ) : (
              <div>
                {mailboxStatus.connected ? (
                  <div className="d-flex align-items-center gap-3 mb-2">
                    <div className="d-flex align-items-center gap-2">
                      <span className="text-success">✓</span>
                      <span>Connected to: <strong>{mailboxStatus.email}</strong></span>
                    </div>
                    <CButton
                      color="danger"
                      size="sm"
                      onClick={handleUnlinkMailbox}
                      disabled={mailboxLoading}
                    >
                      {mailboxLoading ? "Unlinking..." : "Unlink Mailbox"}
                    </CButton>
                  </div>
                ) : (
                  <div className="d-flex align-items-center gap-3 mb-2">
                    <div className="d-flex align-items-center gap-2">
                      <span className="text-warning">⚠️</span>
                      <span>No mailbox connected</span>
                    </div>
                    <CButton
                      color="primary"
                      size="sm"
                      onClick={handleLinkMailbox}
                      disabled={mailboxLoading}
                    >
                      {mailboxLoading ? "Linking..." : "Link Mailbox"}
                    </CButton>
                  </div>
                )}
                <div className="text-muted">
                  <small>
                    Link your email inbox to enable AI-powered email communication with customers.
                    {mailboxStatus.connected && mailboxStatus.connected_at && (
                      <> Connected on {new Date(mailboxStatus.connected_at * 1000).toLocaleDateString()}.</>
                    )}
                  </small>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Voice Selection Section */}
         <div className="mb-4">
            <div className="h4">Voice Selection</div>
            <div className="mb-3">
              <CFormLabel>Select Male Voice:</CFormLabel>
              <div>
                <CFormCheck
                  type="radio"
                  name="maleVoice"
                  id="Eric"
                  label="Eric"
                  value="Eric"
                  // onChange={handleChange}
                />
                <CFormCheck
                  type="radio"
                  name="maleVoice"
                  id="Dan"
                  label="Dan"
                  value="Dan"
                  // onChange={handleChange}
                />
                <CFormCheck
                  type="radio"
                  name="maleVoice"
                  id="Mike"
                  label="Mike"
                  value="Mike"
                  // onChange={handleChange}
                />
              </div>
            </div>
            <div className="mb-3">
              <CFormLabel>Select Female Voice:</CFormLabel>
              <div>
                <CFormCheck
                  type="radio"
                  name="femaleVoice"
                  id="Debby"
                  label="Debby"
                  value="Debby"
                  // onChange={handleChange}
                />
                <CFormCheck
                  type="radio"
                  name="femaleVoice"
                  id="Julia"
                  label="Julia"
                  value="Julia"
                  // onChange={handleChange}
                />
                <CFormCheck
                  type="radio"
                  name="femaleVoice"
                  id="Susan"
                  label="Susan"
                  value="Susan"
                  // onChange={handleChange}
                />
              </div>
            </div>
          </div>

        <CButton
          color="primary"
          type="submit"
          className="mt-4"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Saving...' : 'Save Settings'}
        </CButton>
      </CForm>

      {/* Validation Error Modal */}
      <CModal visible={showValidationModal} onClose={() => setShowValidationModal(false)} alignment="center">
        <CModalHeader>
          <CModalTitle>⚠️ Validation Errors</CModalTitle>
        </CModalHeader>
        <CModalBody>
          <div style={{ whiteSpace: 'pre-line', fontFamily: 'monospace', fontSize: '14px' }}>
            {validationModalMessage}
          </div>
        </CModalBody>
        <CModalFooter>
          <CButton color="primary" onClick={() => setShowValidationModal(false)}>
            OK, I'll Fix These
          </CButton>
        </CModalFooter>
      </CModal>
    </div>
  );
};

export default FormPage;