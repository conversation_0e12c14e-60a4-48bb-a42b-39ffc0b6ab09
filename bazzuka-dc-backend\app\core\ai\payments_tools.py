from app.ai.tools import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>lPara<PERSON>, <PERSON>l, Too<PERSON><PERSON><PERSON>ine

from app.core.payments import PaymentsTool

# setup_payments_and_get_payment_link_tool = Tool(
#     "setup_payments_and_get_payment_link",
#     "Setup the payment plan for a particular issue and return the first payment link to the user's email. This should always be used after all the payment plan details are confirmed and agreed to by the user. Returns the payment link.",
#     PaymentsTool.setup_payments_and_get_payment_link,
#     ToolParams(
#         [
#             ToolParam(
#                 "issue_id",
#                 "string",
#                 "The issue_id of the case associated with the payments.",
#                 required=True,
#             ),
#             ToolParam(
#                 "amounts",
#                 "array",
#                 "An array of numeric amounts of the agreed payment installments in smallest currency unit (e.g., cents for USD). For example, three payments of $300 dollars would look like [30000, 30000, 30000].",
#                 required=True,
#                 items_type="number",
#             ),
#             ToolParam(
#                 "due_dates",
#                 "array",
#                 "An array of strings in the format 'YYYY-MM-DD' describing when the payments are due, for example ['2025-03-01', '2025-04-01', '2025-05-01'].",
#                 required=True,
#                 items_type="string",
#             ),
#         ]
#     ),
# )

# Utility: Extract interval and interval_count from user text
import re

def extract_interval_from_text(text):
    text = text.lower()
    if "every week" in text or "weekly" in text:
        return "week", 1
    if "every two weeks" in text or "biweekly" in text or "bi-weekly" in text or "every 2 weeks" in text:
        return "week", 2
    if "every month" in text or "monthly" in text:
        return "month", 1
    match = re.search(r"every (\d+) week", text)
    if match:
        return "week", int(match.group(1))
    match = re.search(r"every (\d+) month", text)
    if match:
        return "month", int(match.group(1))
    if "one time" in text or "just once" in text or "single payment" in text:
        return None, None
    return None, None

# Enhanced schedule_payment wrapper that can detect payment frequency from context
from functools import wraps
orig_schedule_payment = PaymentsTool.schedule_payment

def enhanced_schedule_payment(self, issue_id, amount, recurring=False, settlement_discount=False, interval=None, interval_count=None, user_text=None, start_date=None, due_date=None):
    # If recurring is True but interval/interval_count are not provided, 
    # try to detect from conversation context or default to monthly
    if recurring and (interval is None or interval_count is None):
        # Check if we can extract from user_text parameter
        if user_text:
            extracted_interval, extracted_count = extract_interval_from_text(user_text)
            if extracted_interval and extracted_count:
                interval = extracted_interval
                interval_count = extracted_count
        
        # If still not set, default to monthly
        if interval is None or interval_count is None:
            interval = "month"
            interval_count = 1
    
    return orig_schedule_payment(self, issue_id, amount, recurring, settlement_discount, interval, interval_count, start_date, due_date)

PaymentsTool.schedule_payment = enhanced_schedule_payment

schedule_payment_tool = Tool(
    "schedule_payment",
    "Schedule a payment. For exact payments (amount = outstanding balance), the payment link is sent immediately. For partial payments and recurring payments, they require human approval first. This should always be used after all the payment plan details are confirmed and agreed to by the user. For recurring payments, specify the interval (week/month) and interval_count (1 for weekly/monthly, 2 for biweekly). CRITICAL: Any payment amount higher than the outstanding balance will be REJECTED immediately.",
    PaymentsTool.schedule_payment,
    ToolParams(
        [
            ToolParam(
                "issue_id",
                "number",
                "The issue_id of the case associated with the payments.",
                required=True,
            ),
            ToolParam(
                "amount",
                "number",
                "The amount of the payment in USD. For example, $300 would be 300. CRITICAL: This amount MUST NOT exceed the outstanding balance or the payment will be REJECTED. Only use amounts equal to or less than the outstanding balance.",
                required=True,
            ),
            ToolParam(
                "recurring",
                "boolean",
                "Whether the payment is recurring. If true, the payment will be scheduled to recur based on interval and interval_count. If false, the payment will be a one-time payment.",
                required=True,
            ),
            ToolParam(
                "start_date",
                "string",
                "Optional: When to begin the payment process in format 'YYYY-MM-DD'. For RECURRING payments: when billing cycle starts. For ONE-TIME payments: when to send the payment link (use only if customer wants delayed sending). If not provided, processes immediately.",
                required=False,
            ),
            ToolParam(
                "due_date",
                "string",
                "The payment deadline in format 'YYYY-MM-DD'. For ONE-TIME payments: when payment must be completed. For RECURRING payments: when each payment in the cycle is due. If not provided, defaults to 7 days from start_date or 7 days from today.",
                required=False,
            ),
            ToolParam(
                "interval",
                "string",
                "The interval for recurring payments. Use 'week' for weekly/biweekly payments, 'month' for monthly payments. Required if recurring is true.",
                required=False,
            ),
            ToolParam(
                "interval_count",
                "number",
                "The number of intervals between payments. Use 1 for weekly/monthly, 2 for biweekly. Required if recurring is true.",
                required=False,
            ),
        ]
    ),
)

get_info_tool = Tool(
    "get_info",
    "Get information about the defaulter. Verification is optional if not configured by the organization.",
    PaymentsTool.get_info,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The defaulter's defaulter_id.",
                required=True,
            ),
            ToolParam(
                "verification_type",
                "string",
                "The type of verification. Only required if the organization has configured verification methods.",
                required=False,
            ),
            ToolParam(
                "verification_value",
                "string",
                "The verification value. Only required if verification_type is provided.",
                required=False,
            ),
        ]
    ),
)

schedule_one_off_communication_tool = Tool(
    "schedule_one_off_communication",
    "Schedule a one-off communication with the defaulter. This should be used if the user requests contact at a future time.",
    PaymentsTool.schedule_one_off_communication,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The defaulter's defaulter_id.",
                required=True,
            ),
            ToolParam(
                "channel",
                "string",
                "The communication channel. This can be 'email' or 'call'.",
                required=True,
            ),
            ToolParam(
                "date",
                "string",
                "The date of the communication in the format 'YYYY-MM-DD'.",
                required=True,
            ),
            ToolParam(
                "time",
                "string",
                "The time of the communication in the format 'HH:MM'.",
                required=True,
            ),
            ToolParam(
                "reason",
                "string",
                "The reason for the communication.",
                required=True,
            ),
            ToolParam(
                "is_human_followup",
                "boolean",
                "Whether the follow-up should be with a human (true) or AI (false). Defaults to false (AI).",
                required=False,
            ),
        ]
    ),
)

get_info_tool_email = Tool(
    "get_info",
    "Get the defaulter's information if they are verified or if verification is not required. Returns all the case information.",
    PaymentsTool.get_info_email,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The defaulter's defaulter_id.",
                required=True,
            ),
        ]
    ),
)

try_verify_tool = Tool(
    "try_verify",
    "Attempt to verify the defaulter's identity and retrieve the defaulter's information.",
    PaymentsTool.try_verify,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The defaulter's defaulter_id.",
                required=True,
            ),
            ToolParam(
                "verification_type",
                "string",
                "The type of verification. For example, 'ssn' for last four digits of social security number or 'dob' for date of birth.",
                required=True,
            ),
            ToolParam(
                "verification_value",
                "string",
                "The verification value. For example, the last four digits of the social security number as a string with exactly four digits or the date of birth parsed as 'MM/DD/YYYY'.",
                required=True,
            ),
        ]
    ),
)

opt_out_of_communications_tool = Tool(
    "opt_out_of_communications",
    "Opt a defaulter out of specific communication channels. This should be used when a user requests to stop receiving communications through certain channels.",
    PaymentsTool.opt_out_of_communications,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The ID of the defaulter requesting the opt-out.",
                required=True,
            ),
            ToolParam(
                "channels",
                "array",
                "List of channels to opt out of. Can include 'calls', 'emails', and/or 'texts'.",
                required=True,
                items_type="string",
            ),
        ]
    ),
)

propose_email_update_tool = Tool(
    "propose_email_update",
    "Propose an email address update for a defaulter during a phone call. The proposed email is stored for review and not immediately applied to the account.",
    PaymentsTool.propose_email_update,
    ToolParams(
        [
            ToolParam(
                "defaulter_id",
                "string",
                "The ID of the defaulter whose email address should be updated.",
                required=True,
            ),
            ToolParam(
                "new_email",
                "string",
                "The new email address to propose for the defaulter.",
                required=True,
            ),
        ]
    ),
)

setup_payment_and_get_link_tool = Tool(
    "schedule_payment",
    "Setup the payment for a particular issue and returns the payment link.",
    PaymentsTool.setup_payment_and_get_link,
    ToolParams(
        [
            ToolParam(
                "issue_id",
                "string",
                "The ID of the issue to setup the payment plan for.",
                required=True,
            ),
            ToolParam(
                "amount",
                "number",
                "The amount of the payment in USD. For example, 300.00 or 1000.50",
                required=True,
            ),
            ToolParam(
                "recurring",
                "boolean",
                "Whether the payment is recurring.",
                required=False,
            ),
            ToolParam(
                "settlement_discount",
                "boolean",
                "Whether the payment is a settlement discount.",
                required=False,
            ),
            # ToolParam(
            #     "due_date",
            #     "string",
            #     "The due date of the payment in the format 'YYYY-MM-DD'.",
            #     required=False,
            # ),
        ]
    )
)

# delete_payment_plan_for_issue_tool = Tool(
#     "delete_payment_plan_for_issue",
#     "Delete a payment plan for a particular issue.",
#     PaymentsTool.delete_payment_plan_for_issue,
#     ToolParams(
#         [
#             ToolParam(
#                 "issue_id",
#                 "string",
#                 "The ID of the issue to delete the payment plan for.",
#                 required=True,
#             ),
#         ]
#     ),
# )

# get_upcoming_payments_tool = Tool(
#     "get_upcoming_payments",
#     "Get the upcoming payments for a particular issue.",
#     PaymentsTool.get_upcoming_payments,
#     ToolParams(
#         [
#             ToolParam(
#                 "from_date",
#                 "string",
#                 "The start date of the range to get the upcoming payments for in the format 'YYYY-MM-DD' (default is the beginning of time).",
#                 required=False,
#             ),
#             ToolParam(
#                 "to_date",
#                 "string",
#                 "The end date of the range to get the upcoming payments for in the format 'YYYY-MM-DD' (default is one year from today).",
#                 required=False,
#             ),
#             ToolParam(
#                 "limit",
#                 "number",
#                 "The maximum number of upcoming payments to return.",
#                 required=False,
#             ),
#         ]
#     ),
# )   

def make_payment_tool_engine():
    engine = ToolEngine(PaymentsTool)
    engine.register(schedule_payment_tool)
    engine.register(get_info_tool)
    # Note: schedule_one_off_communication_tool moved to analyzer
    engine.register(opt_out_of_communications_tool)
    engine.register(propose_email_update_tool)
    # engine.register(delete_payment_plan_for_issue_tool)
    # engine.register(get_upcoming_payments_tool)
    return engine


payment_tool_engine = make_payment_tool_engine()


def make_email_payment_tool_engine():
    engine = ToolEngine(PaymentsTool)
    # engine.register(schedule_payment_tool)
    # engine.register(get_info_tool)
    # Note: schedule_one_off_communication_tool moved to analyzer
    engine.register(get_info_tool_email)
    # engine.register(try_verify_tool)
    # engine.register(opt_out_of_communications_tool)
    # engine.register(delete_payment_plan_for_issue_tool)
    # engine.register(get_upcoming_payments_tool)
    return engine


email_payment_tool_engine = make_email_payment_tool_engine()